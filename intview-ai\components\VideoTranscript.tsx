"use client";
import { useInterview } from "@/context/InterviewContext";

const VideoTranscript = () => {
  const { conversationHistory, currentQuestionScore, totalScore, questionCount } = useInterview();

  return (
    <div className="rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden">
      <div className="flex justify-between items-center mb-5">
        <p className="text-lg font-semibold text-black">Interview Transcript</p>
        <div className="text-xs text-gray-500">
          Score: {totalScore}/100
        </div>
      </div>

      <div className="space-y-4">
        {conversationHistory.map((message, index) => (
          <div key={index} className="mb-4">
            {message.role === 'interviewer' ? (
              <div>
                <p className="text-sm font-semibold text-blue-600 mb-1">
                  Question {Math.floor(index / 2) + 1}:
                </p>
                <p className="text-sm text-gray-700 leading-relaxed">
                  {message.content}
                </p>
              </div>
            ) : (
              <div>
                <p className="text-sm font-semibold text-green-600 mb-1">Answer:</p>
                <p className="text-sm text-gray-700 leading-relaxed">
                  {message.content}
                </p>
                {index === conversationHistory.length - 1 && currentQuestionScore > 0 && (
                  <p className="text-xs text-blue-500 mt-1">
                    Score: {currentQuestionScore}/20
                  </p>
                )}
              </div>
            )}
          </div>
        ))}

        {conversationHistory.length === 0 && (
          <p className="text-sm text-gray-500 italic">
            Interview transcript will appear here as you progress...
          </p>
        )}
      </div>
    </div>
  );
};
export default VideoTranscript;
